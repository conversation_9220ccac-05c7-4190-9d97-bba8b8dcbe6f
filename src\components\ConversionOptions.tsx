import { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { CheckCircle } from 'lucide-react';
import { cn } from '../utils/cn';

interface FormatOption {
  id: string;
  icon: string;
}

interface ConversionOptionsProps {
  selectedFormat: string;
  onFormatChange: (format: string) => void;
}

export default function ConversionOptions({ 
  selectedFormat, 
  onFormatChange 
}: ConversionOptionsProps) {
  const { t } = useTranslation();
  
  const formats = useMemo<FormatOption[]>(() => [
    { id: 'ico', icon: '🖼️' },
    { id: 'png', icon: '🎨' },
    { id: 'jpg', icon: '📷' },
    { id: 'gif', icon: '🎞️' },
    { id: 'webp', icon: '🌐' },
    { id: 'svg', icon: '✨' },
    { id: 'tiff', icon: '📄' },
    { id: 'bmp', icon: '🖥️' },
    { id: 'avif', icon: '🚀' },
  ], []);

  return (
    <div>
      <h3 className="text-lg font-semibold text-primary-900 mb-4 text-center">
        {t('converter.selectFormat')}
      </h3>

      <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-9 gap-3">
        {formats.map((format) => (
          <button
            key={format.id}
            className={cn(
              "relative flex flex-col items-center justify-center p-3 rounded-lg border-2 transition-all duration-300 hover:scale-105",
              selectedFormat === format.id
                ? "border-accent-500 bg-accent-500/10 shadow-lg"
                : "border-dark-border bg-dark-surface hover:border-accent-500 hover:bg-accent-500/5 hover:shadow-md"
            )}
            onClick={() => onFormatChange(format.id)}
          >
            <span className="text-2xl mb-2">{format.icon}</span>
            <span className="text-xs font-semibold text-primary-800">
              {t(`formats.${format.id}`)}
            </span>

            {selectedFormat === format.id && (
              <div className="absolute -top-1 -right-1">
                <CheckCircle className="w-4 h-4 text-accent-500 fill-dark-surface shadow-lg rounded-full" />
              </div>
            )}
          </button>
        ))}
      </div>
    </div>
  );
}