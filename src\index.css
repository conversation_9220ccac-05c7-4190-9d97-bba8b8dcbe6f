@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', sans-serif;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-dark-bg text-primary-900 antialiased;
  }

  ::selection {
    @apply bg-accent-400 text-dark-bg;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-xs font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-accent-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none shadow-sm;
  }

  .btn-primary {
    @apply bg-accent-500 text-dark-bg hover:bg-accent-400 hover:shadow-md;
  }

  .btn-secondary {
    @apply bg-dark-surface text-primary-800 hover:bg-primary-100 border border-dark-border;
  }

  .btn-outline {
    @apply border border-dark-border bg-dark-surface hover:bg-primary-100 text-primary-800 hover:border-accent-500;
  }

  .container-custom {
    @apply px-4 mx-auto max-w-5xl sm:px-6 lg:px-8;
  }

  .drop-area {
    @apply border-2 border-dashed rounded-xl p-6 transition-all duration-300 ease-in-out relative overflow-hidden;
  }

  .drop-area:hover {
    @apply scale-[1.02] border-accent-400;
  }

  .drop-area-active {
    @apply border-accent-500 bg-accent-500/10 scale-105 shadow-2xl;
    animation: pulse-glow 2s ease-in-out infinite;
    background-image: linear-gradient(45deg, transparent 25%, rgba(0, 255, 128, 0.1) 25%, rgba(0, 255, 128, 0.1) 50%, transparent 50%, transparent 75%, rgba(0, 255, 128, 0.1) 75%);
    background-size: 20px 20px;
    animation: pulse-glow 2s ease-in-out infinite, moving-stripes 1s linear infinite;
  }

  .drop-area-reject {
    @apply border-red-500 bg-red-500/10;
    animation: shake 0.5s ease-in-out;
  }

  .drop-area-ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(0, 255, 128, 0.3);
    transform: scale(0);
    animation: ripple 1.5s ease-out infinite;
    pointer-events: none;
  }

  @keyframes pulse-glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(0, 255, 128, 0.3), 0 0 40px rgba(0, 255, 128, 0.1);
      border-color: rgb(0, 255, 128);
    }
    50% {
      box-shadow: 0 0 30px rgba(0, 255, 128, 0.5), 0 0 60px rgba(0, 255, 128, 0.2);
      border-color: rgb(51, 255, 153);
    }
  }

  @keyframes ripple {
    0% {
      transform: scale(0);
      opacity: 1;
    }
    100% {
      transform: scale(4);
      opacity: 0;
    }
  }

  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
  }

  @keyframes rotate-pulse {
    0% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.1); }
    100% { transform: rotate(360deg) scale(1); }
  }

  @keyframes moving-stripes {
    0% { background-position: 0 0; }
    100% { background-position: 20px 20px; }
  }

  .icon-upload {
    transition: all 0.3s ease-in-out;
  }

  .icon-upload-active {
    animation: rotate-pulse 2s ease-in-out infinite;
  }

  .thumbnail-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .card {
    @apply bg-dark-surface rounded-xl shadow-lg border border-dark-border;
  }

  .card-compact {
    @apply p-4;
  }

  .card-normal {
    @apply p-6;
  }
}